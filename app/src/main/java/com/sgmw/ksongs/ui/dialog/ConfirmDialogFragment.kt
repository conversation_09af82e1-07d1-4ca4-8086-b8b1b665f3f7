package com.sgmw.ksongs.ui.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.DialogDeleteBinding

/**
 * @author: 董俊帅
 * @time: 2025/1/15
 * @desc: 确认信息弹框
 */

class ConfirmDialogFragment : BaseBlurDialogFragment(R.layout.dialog_delete) {

    private var confirmCallback: (() -> Unit)? = null

    private val mBinding: DialogDeleteBinding by lazy {
        DialogDeleteBinding.inflate(layoutInflater)
    }

    private var mTitle: String? = null
    private var mMessage: String? = null
    private var mConfirmStr: String? = null
    private var mCancelStr: String? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // 设置点击外部关闭
        isCancelable = true
        dialog?.setCanceledOnTouchOutside(true)
        return mBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
    }

    fun setTitle(title: String) {
        mTitle = title
    }

    fun setMessage(message: String) {
        mMessage = message
    }

    fun setConfirmStr(confirmStr: String) {
        mConfirmStr = confirmStr
    }

    fun setCancelStr(cancelStr: String) {
        mCancelStr = cancelStr
    }

    private fun initView() {
        if (mTitle != null) {
            mBinding.tvTitle.text = mTitle
        }
        if (mMessage != null) {
            mBinding.tvMessage.text = mMessage
        }
        if (mConfirmStr != null) {
            mBinding.tvConfirm.text = mConfirmStr
            mBinding.tvConfirm.contentDescription = mConfirmStr
        }
        if (mCancelStr != null) {
            mBinding.tvCancel.text = mCancelStr
            mBinding.tvCancel.contentDescription = mCancelStr
        }
        mBinding.dialogContent.setOnSingleClickListener {}
        mBinding.root.setOnSingleClickListener {
            dismiss()
        }
        mBinding.ivBack.setOnSingleClickListener {
            dismiss()
        }
        mBinding.tvCancel.setOnSingleClickListener {
            dismiss()
        }
        mBinding.tvConfirm.setOnSingleClickListener {
            confirmCallback?.invoke()
        }
    }

    fun setOnConfirmListener(listener: () -> Unit) {
        confirmCallback = listener
    }

    companion object {
        private const val TAG = "ConfirmDialogFragment"
    }

}