package com.sgmw.ksongs.ui.collect

import android.os.Bundle
import android.view.View
import androidx.navigation.fragment.findNavController
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.FragmentCollectBinding
import com.sgmw.ksongs.track.BigDataConstants.SPLIT
import com.sgmw.ksongs.ui.adapter.CollectAdapter
import com.sgmw.ksongs.ui.dialog.ConfirmDialogFragment
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import com.sgmw.ksongs.utils.NavigationUtils
import com.sgmw.ksongs.widget.AccessibilityLinearLayoutManager

/**
 * 收藏
 */
class CollectFragment: BaseFrameFragment<FragmentCollectBinding, CollectViewModel>() {


    private val adapter = CollectAdapter()

    companion object{
        private const val CARD_NAME  = "cardName"
        fun createBundle(cardName: String): Bundle {
            return Bundle().apply {
                putString(CARD_NAME, cardName)
            }
        }
    }

    override fun FragmentCollectBinding.initView() {
        rvCollect.layoutManager = AccessibilityLinearLayoutManager(context)
        rvCollect.adapter = adapter
        val cardName = arguments?.getString(CARD_NAME)?:""
        adapter.setCardName(cardName)
        adapter.onSelectChangeListener = {
            val selectInfo = adapter.hasSelected()
            tvDelete.isEnabled = selectInfo.first
            updateSelectAllButtonText()
        }

        ivBack.setOnSingleClickListener {
            findNavController().popBackStack()
        }

        tvCancel.setOnSingleClickListener {
            mViewModel?.setEditMode(false)
        }

        tvDelete.setOnSingleClickListener {
            if (adapter.hasSelected().first) {
                showDeleteConfirmDialog()
            }
        }

        tvEdit.setOnSingleClickListener {
            mViewModel?.setEditMode(true)
        }

        tvSelectAll.setOnClickListener {
            handleSelectAllClick()
        }

        adapter.setOnItemClickListener { _, view, position ->
            if (mViewModel?.editMode?.value == true) {
                adapter.selectOrNot(position)
            } else {
                KaraokePlayerManager.playSong(this@CollectFragment, adapter.getItem(position).songInfo,cardName + SPLIT +tvTitle.text.toString())
            }
        }
    }

    private fun setEmptyClickListener() {
        mBinding?.stateLayout?.setEmptyClickListener {
            NavigationUtils.navigateSafely(findNavController(), R.id.action_collect_to_search)
        }
    }

    private fun showDeleteConfirmDialog() {
        val fragment = requireActivity().supportFragmentManager.findFragmentByTag(ConfirmDialogFragment::class.simpleName)
        if (fragment == null) {
            val confirmDialogFragment = ConfirmDialogFragment()
            confirmDialogFragment.setTitle(getString(R.string.confirm_delete_title))
            confirmDialogFragment.setMessage(getString(R.string.confirm_delete_msg))
            confirmDialogFragment.show(requireActivity().supportFragmentManager, ConfirmDialogFragment::class.simpleName)
            confirmDialogFragment.setOnConfirmListener {
                val selectList = adapter.getAllSelect()
                mViewModel?.deleteSelectSongInfo(selectList)
                mViewModel?.setEditMode(false)
                confirmDialogFragment.dismiss()
            }
        }
    }

    override fun initObserve() {
        super.initObserve()
        mViewModel?.collectSongsAfterUpdateStatus?.observe(this) {
            adapter.setList(it)
            mViewModel?.setEditMode(mViewModel?.editMode?.value == true && it.isNotEmpty())
            if (it.isNullOrEmpty()) {
                mBinding?.stateLayout?.showEmpty()
                setEmptyClickListener()
            } else {
                mBinding?.stateLayout?.showContent()
            }
        }
        mViewModel?.collectSongs?.observe(this) {
            mViewModel?.updateDemandStatus(it)
        }
        mViewModel?.demandSongInfo?.observe(this) {
            if (adapter.data.isNotEmpty()) {
                mViewModel?.updateDemandStatus(adapter.data)
            }
        }
        mViewModel?.editMode?.observe(this) {
            changeTitleEditUI(it, adapter.itemCount == 0)
            adapter.updateEditModeUI(it)
        }
        KaraokeConsole.playState.observe(this) {
            adapter.notifyDataSetChanged()
        }
    }

    private fun changeTitleEditUI(isEdit: Boolean, isEmptyList: Boolean) {
        if (isEdit && !isEmptyList) {
            mBinding?.tvEdit?.visibility = View.GONE
            mBinding?.tvSelectAll?.visibility = View.VISIBLE
            mBinding?.tvDelete?.visibility = View.VISIBLE
            mBinding?.tvCancel?.visibility = View.VISIBLE
            updateSelectAllButtonText()
        } else {
            if (isEmptyList) {
                mBinding?.tvEdit?.visibility = View.GONE
            } else {
                mBinding?.tvEdit?.visibility = View.VISIBLE
            }
            mBinding?.tvSelectAll?.visibility = View.GONE
            mBinding?.tvDelete?.visibility = View.GONE
            mBinding?.tvCancel?.visibility = View.GONE
        }
        handleRegisterHotWord(isEdit, isEmptyList)
    }

    /**
     * 处理全选按钮点击事件
     */
    private fun handleSelectAllClick() {
        when (adapter.getSelectState()) {
            0, 1 -> { // 全不选或部分选中时，执行全选
                adapter.selectAllOrNot(true)
            }
            2 -> { // 全选时，执行全不选
                adapter.selectAllOrNot(false)
            }
        }
    }

    /**
     * 处理注册热词
     */
    private fun handleRegisterHotWord(isEdit: Boolean, isEmptyList: Boolean) {
        if (isEdit && !isEmptyList) {
            // 编辑模式下，且列表不为空时，注册热词
            mBinding?.apply {
                tvCancel.contentDescription = getString(R.string.cancel_content_description)
                tvDelete.contentDescription = getString(R.string.delete_content_description)
                tvSelectAll.contentDescription = getString(R.string.select_all_content_description)
                tvEdit.contentDescription = getString(R.string.null_content_description)
            }
        } else if (!isEdit && !isEmptyList) {
            // 非编辑模式下，且列表不为空时，注册热词
            mBinding?.apply {
                tvCancel.contentDescription = getString(R.string.cancel_content_description)
                tvDelete.contentDescription = getString(R.string.delete_content_description)
                tvSelectAll.contentDescription = getString(R.string.select_all_content_description)
                tvEdit.contentDescription = getString(R.string.edit_content_description)
            }
        } else {
            // 非编辑模式下,且列表为空时，注册热词
            mBinding?.apply {
                tvCancel.contentDescription = getString(R.string.null_content_description)
                tvDelete.contentDescription = getString(R.string.null_content_description)
                tvSelectAll.contentDescription = getString(R.string.null_content_description)
                tvEdit.contentDescription = getString(R.string.null_content_description)
            }
        }
    }

    /**
     * 更新全选按钮文案
     */
    private fun updateSelectAllButtonText() {
        mBinding?.tvSelectAll?.text = when (adapter.getSelectState()) {
            0, 1 -> getString(R.string.select_all) // 全不选或部分选中时显示"全选"
            2 -> getString(R.string.select_none) // 全选时显示"全不选"
            else -> getString(R.string.select_all)
        }
    }


    override fun initRequestData() {

    }



}